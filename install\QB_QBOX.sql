ALTER TABLE `player_vehicles`
   ADD IF NOT EXISTS `mileage` int(11) NOT NULL DEFAULT 0,
   ADD IF NOT EXISTS `engineOil` int(11) NOT NULL DEFAULT 1000,
   ADD IF NOT EXISTS `gearOil` int(11) NOT NULL DEFAULT 1000,
   ADD IF NOT EXISTS `brakes` int(11) NOT NULL DEFAULT 1000,
   ADD IF NOT EXISTS `history` longtext DEFAULT NULL,
   ADD IF NOT EXISTS `nitroSystem` int(1) DEFAULT 0,
   ADD IF NOT EXISTS `nitro` int(11) DEFAULT 0;

CREATE TABLE IF NOT EXISTS `mechanic_outfits` (
  `id` int(11) NOT NULL,
  `job` varchar(30) NOT NULL,
  `grade` longtext NOT NULL,
  `label` varchar(60) NOT NULL,
  `gender` varchar(10) NOT NULL,
  `license` longtext DEFAULT 'none',
  `requirements` varchar(50) NOT NULL,
  `skin` longtext NOT NULL
);

ALTER TABLE `mechanic_outfits`
  ADD PRIMARY KEY IF NOT EXISTS (`id`);

ALTER TABLE `mechanic_outfits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;