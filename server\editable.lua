lib.callback.register('p_mechanicjob/server_outfits/fetchJobData', function(source)
    local _source = source
    local xPlayer = Core.GetPlayerFromId(_source)
    local serverData = {
        grades = {},
        jobName = Core.GetPlayerJob(xPlayer).name,
        licenses = {}
    }
    if GetResourceState('piotreq_lst') == 'started' then
        local licenses = exports['piotreq_lst']:getLicenses()
        for k, v in pairs(licenses) do
            serverData.licenses[#serverData.licenses + 1] = {
                value = k,
                label = v.label
            }
        end
    end
    if Config.Framework == 'ESX' then
        local job_grades = MySQL.query.await('SELECT * FROM job_grades')
        for i = 1, #job_grades, 1 do
            local job_grade = job_grades[i]
            if job_grade.job_name == serverData.jobName then
                serverData.grades[#serverData.grades + 1] = {
                    value = tostring(job_grade.grade),
                    label = job_grade.label
                }
            end
        end
    elseif Config.Framework == 'QB' then
        local QBCore = exports['qb-core']:GetCoreObject()
        for jobName, jobData in pairs(QBCore.Shared.Jobs) do
            for grade, gradeInfo in pairs(jobData.grades) do
                if jobName == serverData.jobName then
                    serverData.grades[#serverData.grades + 1] = {
                        value = tostring(grade),
                        label = gradeInfo.name
                    }
                end
            end
        end
    elseif Config.Framework == 'QBOX' then
        for jobName, jobData in pairs(exports['qbx_core']:GetJobs()) do
            for grade, gradeInfo in pairs(jobData.grades) do
                if jobName == serverData.jobName then
                    serverData.grades[#serverData.grades + 1] = {
                        value = tostring(grade),
                        label = gradeInfo.name
                    }
                end
            end
        end
    end

    return serverData
end)

RegisterNetEvent('p_mechanicjob/server_outfits/createOutfit', function(data, skin)
    local _source = source
    local grades = {}
    local licenses = nil
    for i = 1, #data[2], 1 do
        grades[tostring(data[2][i])] = true
    end
    if data[5] then
        licenses = {}
        for i = 1, #data[5], 1 do
            licenses[data[5][i]] = true
        end
    end
    local id = MySQL.insert.await(
        'INSERT INTO mechanic_outfits (job, grade, label, gender, license, requirements, skin) VALUES (@job, @grade, @label, @gender, @license, @requirements, @skin)', {
        ['@job'] = data[1],
        ['@grade'] = json.encode(grades),
        ['@label'] = data[3],
        ['@gender'] = data[4],
        ['@license'] = licenses and json.encode(licenses) or 'none',
        ['@requirements'] = data[6],
        ['@skin'] = json.encode(skin)
    })
    if id then
        Core.ShowNotification(_source, locale('outfit_created', data[3], data[1]), 'success')
    end
end)

lib.callback.register('p_mechanicjob/server_outfits/getOutfits', function(source, playerGender)
    local _source = source
    local xPlayer = Core.GetPlayerFromId(_source)
    local playerJob = Core.GetPlayerJob(xPlayer)
    local outfits = {}
    local result = MySQL.query.await('SELECT * FROM mechanic_outfits WHERE job = ?', {playerJob.name})
    for i = 1, #result, 1 do
        local outfit = result[i]
        if playerGender == outfit.gender then
            local grades = json.decode(outfit.grade)
            local licenses = outfit.license ~= 'none' and json.decode(outfit.license) or nil
            local hasGrade, hasLicense = grades[tostring(playerJob.grade)], false
            if (outfit.requirements == 'required_grade' or outfit.requirements == 'required_both') and not hasGrade then
                goto skip
            end
            
            if outfit.requirements == 'required_license' or outfit.requirements == 'required_both' then
                if licenses then
                    local plyLicenses = MySQL.query.await('SELECT * FROM lst_licenses WHERE owner = ?', {xPlayer.identifier})
                    for j = 1, #plyLicenses, 1 do
                        if licenses[plyLicenses[j].type] then
                            hasLicense = true
                            break
                        end
                    end
                end
    
                if not hasLicense then
                    goto skip
                end
            end
    
            outfits[#outfits + 1] = {
                label = outfit.label,
                skin = outfit.skin
            }
        end

        ::skip::
    end
    return outfits
end)

if Config.Framework ~= 'ESX' then
    lib.callback.register('p_mechanicjob/server/getSkin', function(source)
        local Player = Core.GetPlayerFromId(source)
        local result = MySQL.query.await('SELECT * FROM playerskins WHERE citizenid = ? AND active = ?', {Player.PlayerData.citizenid, 1})
        if result and result[1] and result[1].skin then
            return json.decode(result[1].skin)
        end

        return nil
    end)
end