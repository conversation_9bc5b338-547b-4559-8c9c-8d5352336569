if Config.Framework ~= 'QBOX' then
    return
end

Core.GetPlayerFromId = function(playerId)
    return exports['qbx_core']:GetPlayer(playerId)
end

Core.GetPlayerJob = function(xPlayer)
    local xPlayer = type(xPlayer) == 'number' and Core.GetPlayerFromId(xPlayer) or xPlayer
    local playerJob = xPlayer.PlayerData.job
    return {
        name = playerJob.name,
        grade = tonumber(playerJob.grade.level),
        label = playerJob.label
    }
end

Core.ShowNotification = function(playerId, text)
    TriggerClientEvent('ox_lib:notify', playerId, {
        title = 'Notification',
        text = text,
        type = 'inform',
    })
end

Core.GetPlayers = function()
    return exports['qbx_core']:GetQBPlayers()
end

RegisterNetEvent('QBCore:Server:PlayerLoaded', function(xPlayer)
    playerLoaded(xPlayer.PlayerData.source)
end)

Core.Inventory = {
    getItemCount = function(playerId, itemName)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:Search(playerId, 'count', itemName)
        elseif GetResourceState('qs-inventory') == 'started' then
            return exports['qs-inventory']:GetItemTotalAmount(playerId, itemName)
        end
    end,
    getItemData = function(playerId, itemName)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:Search(playerId, 'slots', itemName)
        elseif GetResourceState('qs-inventory') == 'started' then
            local foundedItem = nil
            local inventory = exports['qs-inventory']:GetInventory(playerId)
            for k, v in pairs(inventory) do
                if k == itemName then
                    foundedItem = v
                    break 
                end
            end
            return foundedItem
        end
    end,
    addItem = function(playerId, itemName, itemCount, itemData)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:AddItem(playerId, itemName, itemCount, itemData)
        elseif GetResourceState('qs-inventory') == 'started' then
            return exports['qs-inventory']:AddItem(playerId, itemName, itemCount, nil, itemData)
        end
    end,
    removeItem = function(playerId, itemName, itemCount, itemData)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:RemoveItem(playerId, itemName, itemCount, itemData)
        elseif GetResourceState('qs-inventory') == 'started' then
            return exports['qs-inventory']:RemoveItem(playerId, itemName, itemCount, nil, itemData)
        end
    end,
    registerStash = function(name, label, slots, weight, owner, job, coords)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:RegisterStash(name, label, slots, weight, owner, job, coords)
        end
    end,
    clearInventory = function(stashId, keep)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:ClearInventory(stashId, keep)
        elseif GetResourceState('qs-inventory') == 'started' then
            local stashItems = exports['qs-inventory']:GetStashItems(stashId)
            for k, v in pairs(stashItems) do
                exports['qs-inventory']:RemoveItemIntoStash(stashId, itemData.name, itemData.amount, k)
            end
            return true
        end
    end,
    setMetadata = function(playerid, slot, metadata)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:SetMetadata(playerid, slot, metadata)
        elseif GetResourceState('qs-inventory') == 'started' then
            return exports['qs-inventory']:SetItemMetadata(playerid, slot, metadata)
        end
    end,
}

Core.UpdateNitro = function(nitroValue, vehiclePlate)
    MySQL.update('UPDATE player_vehicles SET nitro = ? WHERE plate = ?', {nitroValue, vehiclePlate})
end

Core.GetDatabaseNitro = function(vehiclePlate)
    local row = MySQL.single.await('SELECT nitro FROM player_vehicles WHERE plate = ?', {vehiclePlate})
    return row and row.nitro or 0
end

Core.hasNitroSystem = function(vehiclePlate)
    local result = MySQL.single.await('SELECT nitroSystem FROM player_vehicles WHERE plate = ?', {vehiclePlate})
    if result and result.nitroSystem and result.nitroSystem == 1 then
        return true
    end

    return false
end

Core.UpdateNitroSystem = function(value, vehiclePlate)
    MySQL.update('UPDATE player_vehicles SET nitroSystem = ? WHERE plate = ?', {value, vehiclePlate})
end

Core.GetVehicleData = function(vehiclePlate)
    local row = MySQL.single.await('SELECT * FROM player_vehicles WHERE plate = ?', {vehiclePlate})
    return row or nil
end

Core.UpdateVehicleData = function(vehiclePlate, data)
    MySQL.update('UPDATE player_vehicles SET mileage = ?, engineOil = ?, gearOil = ?, brakes = ? WHERE plate = ?', {
        data.mileage,
        data.engineOil,
        data.gearOil,
        data.brakes,
        vehiclePlate
    })
end

Core.GetVehicleHistory = function(vehiclePlate)
    local row = MySQL.single.await('SELECT history FROM player_vehicles WHERE plate = ?', {vehiclePlate})
    if row and row.history then
        row.history = json.decode(row.history)
    else
        row = {history = {}}
    end

    return row.history
end

Core.UpdateVehicleOil = function(oilType, history, value, vehiclePlate)
    MySQL.update('UPDATE player_vehicles SET '..oilType..' = ?, history = ? WHERE plate = ?', {value, history, vehiclePlate})
end

Core.UpdateVehicleBrakes = function(brakes, history, vehiclePlate)
    MySQL.update('UPDATE player_vehicles SET brakes = ?, history = ? WHERE plate = ?', {brakes, history, vehiclePlate})
end