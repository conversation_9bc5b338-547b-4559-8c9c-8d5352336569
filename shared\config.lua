Config = {}

Config.Debug = false
Config.Framework = 'ESX' -- ESX / QB / QBOX
Config.Language = 'en' -- en / pl
Config.MechanicMDT = GetResourceState('piotreq_lst') ~= 'missing'

Config.TrimPlate = function(plate)
    return (string.gsub(plate, "^%s*(.-)%s*$", "%1"))
end

Config.Workshops = {
    ['lscustoms'] = {
        coords = vector3(-371.6889, -118.0355, 38.6867),
        size = vector3(85.0, 85.0, 60.0),
        rotation =  61.3904,
        blip = {
            label = 'LS Customs',
            sprite = 402,
            color = 44,
            scale = 0.9
        }
    },
    ['bennys'] = {
        coords = vector3(831.4695, -1998.4679, 29.3420),
        size = vector3(85.0, 85.0, 60.0),
        rotation =  61.3904,
        blip = {
            label = 'Benny\'S',
            sprite = 402,
            color = 44,
            scale = 0.9
        }
    },
    ['mecano'] = {
        coords = vector3(562.1942, 2747.3374, 42.1266),
        size = vector3(85.0, 85.0, 60.0),
        rotation =  1.3904,
        blip = {
            label = 'Garage Nord',
            sprite = 402,
            color = 44,
            scale = 0.9
        }
    }
}

Config.Repairs = {
    engineTime = 7500,
    bodyTime = 7500,
    tireTime = 15000,
    oilTime = 7500,
    brakesTime = 7500,
    nitroTime = 15000,
    cleanTime = 5000,
    checkTime = 5000
}

Config.OutfitsAccess = {
    ['lscustoms'] = 0,
    ['bennys'] = 0,
    ['mecano'] = 0
}

Config.Radio = {
    [4] = { -- frequency
        jobs = {['lscustoms'] = true},
        label = "LS Customs"
    },
    [5] = {
        jobs = {['mecano'] = true},
        label = "Mecano Nord"
    },
    [6] = {
        jobs = {['bennys'] = true},
        label = "Bennys"
    },
}

Config.Parts = {
    [0] = { -- for vehicle class Compacts
        engine = { -- for engine
            [{0, 1000}] = { -- engine health 0-100
                [1] = {part = 'parts_vip', count = 1} -- need this items to repair
            }
        },
        body = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        engineOil = 'engine_oil', -- item name
        gearOil = 'gear_oil',
        brakeSystem = 'brake_system'
    },
    [1] = {
        engine = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        body = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        engineOil = 'engine_oil',
        gearOil = 'gear_oil',
        brakeSystem = 'brake_system'
    },
    [2] = {
        engine = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        body = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        engineOil = 'engine_oil',
        gearOil = 'gear_oil',
        brakeSystem = 'brake_system'
    },
    [3] = {
        engine = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        body = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        engineOil = 'engine_oil',
        gearOil = 'gear_oil',
        brakeSystem = 'brake_system'
    },
    [4] = {
        engine = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        body = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        engineOil = 'engine_oil',
        gearOil = 'gear_oil',
        brakeSystem = 'brake_system'
    },
    [5] = {
        engine = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        body = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        engineOil = 'engine_oil',
        gearOil = 'gear_oil',
        brakeSystem = 'brake_system'
    },
    [6] = {
        engine = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        body = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        engineOil = 'engine_oil',
        gearOil = 'gear_oil',
        brakeSystem = 'brake_system'
    },
    ['sultanrs'] = { -- for specific model
        engine = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        body = {
            [{0, 1000}] = {
                [1] = {part = 'parts_vip', count = 1}
            }
        },
        engineOil = 'engine_oil', -- item name
        gearOil = 'gear_oil',
        brakeSystem = 'brake_system'
    }
}

Config.Blacklist = { -- for flatbed
    [10] = true, -- Industrial
    [13] = true, -- Cycles
    [14] = true, -- Boats
    [15] = true, -- Helicopters
    [16] = true, -- Planes
    [17] = true, -- Service
    [20] = true, -- Commercial
    [21] = true -- Trains
}

Config.Flatbeds = {
    {
        model = "flatbed3",
        Extras = {
            [1] = false 
        },
        Attach = vector2(0.0, 1.0),
        Radius = 3.0,
        Default = {
            Pos = vector3(0.0, -3.8, 0.35),
            Rot = vector3(0.0, 0.0, 0.0)
        },
        Active = {
            Pos = vector3(0.0, -8.20, -0.75),
            Rot = vector3(15.0, 0.0, 0.0)
        }
    }
}