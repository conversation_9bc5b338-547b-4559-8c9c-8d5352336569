	["nos"] = {
		label = "Butla NoS",
		weight = 1500,
		stack = false,
		close = true,
		client = {
			disable = { move = true, car = true, combat = true },
			usetime = 5000,
			cancel = true,
			export = "p_mechanicjob.nos"
		}
	},

	['nos_system'] = {
		label = "NoS System",
		weight = 5000,
		stack = false,
		close = false,
	},

	['flatbed_remote'] = {
		label = 'Pilot',
		weight = 100,
		stack = false,
		close = true,
		consume = 0,
		client = {
			export = 'p_mechanicjob.remote'
		}
	},

	['weld_torch'] = {
		label = 'Palnik',
		weight = 750,
		stack = false,
		consume = 0,
		degrade = 3600,
		decay = true
	},
	
	['rug'] = {
		label = 'Ręcznik',
		weight = 100,
		stack = false,
		degrade = 3600,
		decay = true
	},

	['tire'] = {
		label = 'Opona',
		weight = 1000,
		stack = false,
		consume = 0,
		client = {
			add = function()
				exports['p_mechanicjob']:ToggleProp(true, 'prop_rub_tyre_01', {0.0, 0.3, 0.25}, {150.0, 0.0, 45.0})
			end,
			remove = function(total)
				if total < 1 then
					exports['p_mechanicjob']:ToggleProp(false)
				end
			end
		}
	},

    ['parts_default'] = {
		label = 'Części podstawowe',
		weight = 200,
		stack = true,
		consume = 0
	},

	['parts_advanced'] = {
		label = 'Części zaawansowane',
		weight = 200,
		stack = true,
		consume = 0
	},
	
	['parts_premium'] = {
		label = 'Części premium',
		weight = 200,
		stack = true,
		consume = 0
	},

	['parts_vip'] = {
		label = 'Części VIP',
		weight = 200,
		stack = true,
		consume = 0
	},

    ['engine_oil'] = {
		label = 'Olej silnikowy',
		weight = 500,
		stack = true,
		close = false,
		consume = 0,
		durability = 100
	},

	['gear_oil'] = {
		label = 'Olej skrzyni biegów',
		weight = 500,
		stack = true,
		close = false,
		consume = 0,
		durability = 100
	},

	['brake_system'] = {
		label = 'Układ hamulcowy',
		weight = 1500,
		stack = false,
		close = false,
		consume = 0,
	},