["nos"] = {
        name = 'nos',
        label = "NoS Bottle",
        weight = 1500,
        unique = true,
        shouldClose = true,
        image = "nos.png",
        type = 'item',
        useable = true,
        client = {
            disable = { move = true, car = true, combat = true },
            usetime = 5000,
            cancel = true,
            export = "p_mechanicjob.nos"
        }
    },

    ['nos_system'] = {
        name = 'nos_system',
        label = "NoS System",
        weight = 5000,
        unique = true,
        shouldClose = false,
        image = "nos_system.png",
        type = 'item',
    },

    ['flatbed_remote'] = {
        name = 'flatbed_remote',
        label = 'Flatbed remote',
        weight = 100,
        unique = true,
        shouldClose = true,
        image = "flatbed_remote.png",
        type = 'item',
        useable = true,
        client = {
            export = 'p_mechanicjob.remote'
        }
    },

    ['weld_torch'] = {
        name = 'weld_torch',
        label = 'Weld torch',
        weight = 750,
        unique = true,
        shouldClose = true,
        image = "weld_torch.png",
        type = 'item',
    },
    
    ['rug'] = {
        name = 'rug',
        label = 'Rug',
        weight = 100,
        unique = true,
        shouldClose = true,
        image = "rug.png",
        type = 'item',
    },

    ['tire'] = {
        name = 'tire',
        label = 'Tire',
        weight = 1000,
        unique = true,
        shouldClose = true,
        image = "tire.png",
        type = 'item',
        client = {
            add = function()
                exports['p_mechanicjob']:ToggleProp(true, 'prop_rub_tyre_01', {0.0, 0.3, 0.25}, {150.0, 0.0, 45.0})
            end,
            remove = function(total)
                if total < 1 then
                    exports['p_mechanicjob']:ToggleProp(false)
                end
            end
        }
    },

    ['parts_default'] = {
        name = 'parts_default',
        label = 'Default Parts',
        weight = 200,
        unique = false,
        shouldClose = true,
        image = "parts_default.png",
        type = 'item',
    },

    ['parts_advanced'] = {
        name = 'parts_advanced',
        label = 'Advanced Parts',
        weight = 200,
        unique = false,
        shouldClose = true,
        image = "parts_advanced.png",
        type = 'item',
    },
    
    ['parts_premium'] = {
        name = 'parts_premium',
        label = 'Premium Parts',
        weight = 200,
        unique = false,
        shouldClose = true,
        image = "parts_premium.png",
        type = 'item',
    },

    ['parts_vip'] = {
        name = 'parts_vip',
        label = 'VIP Parts',
        weight = 200,
        unique = false,
        shouldClose = true,
        image = "parts_vip.png",
        type = 'item',
    },

    ['engine_oil'] = {
        name = 'engine_oil',
        label = 'Engine oil',
        weight = 500,
        unique = false,
        shouldClose = false,
        image = "engine_oil.png",
        type = 'item',
    },

    ['gear_oil'] = {
        name = 'gear_oil',
        label = 'Gear oil',
        weight = 500,
        unique = false,
        shouldClose = false,
        image = "gear_oil.png",
        type = 'item',
    },

    ['brake_system'] = {
        name = 'brake_system',
        label = 'Brakes System',
        weight = 1500,
        unique = true,
        shouldClose = false,
        image = "brake_system.png",
        type = 'item',
    },