if Config.Framework ~= 'ESX' then
    return
end

local ESX = exports['Framework']:getSharedObject()

Core.GetPlayerFromId = function(playerId)
    return ESX.GetPlayerFromId(playerId)
end

Core.GetPlayerJob = function(xPlayer)
    return xPlayer.job
end

Core.ShowNotification = function(playerId, text)
    TriggerClientEvent('esx:showNotification', playerId, text)
end

Core.GetPlayers = function()
    return ESX.GetPlayers()
end

RegisterNetEvent('esx:playerLoaded', function(player)
    playerLoaded(player)
end)

Core.Inventory = {
    getItemCount = function(playerId, itemName)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:Search(playerId, 'count', itemName)
        elseif GetResourceState('qs-inventory') == 'started' then
            return exports['qs-inventory']:GetItemTotalAmount(playerId, itemName)
        end
    end,
    getItemData = function(playerId, itemName)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:Search(playerId, 'slots', itemName)
        elseif GetResourceState('qs-inventory') == 'started' then
            local foundedItem = nil
            local inventory = exports['qs-inventory']:GetInventory(playerId)
            for k, v in pairs(inventory) do
                if k == itemName then
                    foundedItem = v
                    break 
                end
            end
            return foundedItem
        end
    end,
    addItem = function(playerId, itemName, itemCount, itemData)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:AddItem(playerId, itemName, itemCount, itemData)
        elseif GetResourceState('qs-inventory') == 'started' then
            return exports['qs-inventory']:AddItem(playerId, itemName, itemCount, nil, itemData)
        end
    end,
    removeItem = function(playerId, itemName, itemCount, itemData)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:RemoveItem(playerId, itemName, itemCount, itemData)
        elseif GetResourceState('qs-inventory') == 'started' then
            return exports['qs-inventory']:RemoveItem(playerId, itemName, itemCount, nil, itemData)
        end
    end,
    registerStash = function(name, label, slots, weight, owner, job, coords)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:RegisterStash(name, label, slots, weight, owner, job, coords)
        end
    end,
    clearInventory = function(stashId, keep)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:ClearInventory(stashId, keep)
        elseif GetResourceState('qs-inventory') == 'started' then
            local stashItems = exports['qs-inventory']:GetStashItems(stashId)
            for k, v in pairs(stashItems) do
                exports['qs-inventory']:RemoveItemIntoStash(stashId, itemData.name, itemData.amount, k)
            end
            return true
        end
    end,
    setMetadata = function(playerid, slot, metadata)
        if GetResourceState('ox_inventory') == 'started' then
            return exports['ox_inventory']:SetMetadata(playerid, slot, metadata)
        elseif GetResourceState('qs-inventory') == 'started' then
            return exports['qs-inventory']:SetItemMetadata(playerid, slot, metadata)
        end
    end,
}

Core.UpdateNitro = function(nitroValue, vehiclePlate)
    MySQL.update('UPDATE owned_vehicles SET nitro = ? WHERE plate = ?', {nitroValue, vehiclePlate})
end

Core.GetDatabaseNitro = function(vehiclePlate)
    local row = MySQL.single.await('SELECT nitro FROM owned_vehicles WHERE plate = ?', {vehiclePlate})
    return row and row.nitro or 0
end

Core.hasNitroSystem = function(vehiclePlate)
    local result = MySQL.single.await('SELECT nitroSystem FROM owned_vehicles WHERE plate = ?', {vehiclePlate})
    if result and result.nitroSystem and result.nitroSystem == 1 then
        return true
    end

    return false
end

Core.UpdateNitroSystem = function(value, vehiclePlate)
    MySQL.update('UPDATE owned_vehicles SET nitroSystem = ? WHERE plate = ?', {value, vehiclePlate})
end

Core.GetVehicleData = function(vehiclePlate)
    local row = MySQL.single.await('SELECT * FROM owned_vehicles WHERE plate = ?', {vehiclePlate})
    return row or nil
end

Core.UpdateVehicleData = function(vehiclePlate, data)
    MySQL.update('UPDATE owned_vehicles SET mileage = ?, engineOil = ?, gearOil = ?, brakes = ? WHERE plate = ?', {
        data.mileage,
        data.engineOil,
        data.gearOil,
        data.brakes,
        vehiclePlate
    })
end

Core.GetVehicleHistory = function(vehiclePlate)
    local row = MySQL.single.await('SELECT history FROM owned_vehicles WHERE plate = ?', {vehiclePlate})
    if row and row.history then
        row.history = json.decode(row.history)
    else
        row = {history = {}}
    end

    return row.history
end

Core.UpdateVehicleOil = function(oilType, history, value, vehiclePlate)
    MySQL.update('UPDATE owned_vehicles SET '..oilType..' = ?, history = ? WHERE plate = ?', {value, history, vehiclePlate})
end

Core.UpdateVehicleBrakes = function(brakes, history, vehiclePlate)
    MySQL.update('UPDATE owned_vehicles SET brakes = ?, history = ? WHERE plate = ?', {brakes, history, vehiclePlate})
end