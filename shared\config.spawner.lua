Config.Spawner = {}

Config.Spawner.Spawners = {
    ['mechanic'] = {
        [1] = {
            coords = vector4(-347.2022, -134.5282, 39.0422, 165.7677),
            spawnCoords = vector4(-346.5867, -145.7935, 39.0097, 55.7884),
            ped = 's_m_y_airworker',
            vehicles = {
                ['flatbed3'] = 'flatbed', -- model name from base game for photo
                ['DLF450'] = 'test', -- model name from base game for photo
            }
        }
    },
    ['bennys'] = {
        [1] = {
            coords = vector4(839.8899, -1980.1351, 29.3444, 181.5592),
            spawnCoords = vector4(842.6530, -1984.7096, 29.3445, 94.5705),
            ped = 's_m_y_airworker',
            vehicles = {
                ['flatbed3'] = 'flatbed' -- model name from base game for photo
            }
        }
    },
    ['mecano'] = {
        [1] = {
            coords = vector4(555.4451, 2741.0698, 42.0588, 187.1903),
            spawnCoords = vector4(558.2333, 2734.1199, 42.0599, 177.3751),
            ped = 's_m_y_airworker',
            vehicles = {
                ['flatbed3'] = 'flatbed' -- model name from base game for photo
            }
        }
    },
    
    
}

Config.Spawner.AddKeys = function(plate, vehicle, model)
    SetVehicleEngineOn(vehicle, true, true, false)
    -- TriggerServerEvent('p_carkeys:AddKeys', plate)
    -- TriggerServerEvent('giveremote', plate)
end

Config.Spawner.RemoveKeys = function(plate, vehicle, model)
    -- TriggerServerEvent('p_carkeys:RemoveKeys', plate)
    -- TriggerServerEvent('removeremote', plate)
end